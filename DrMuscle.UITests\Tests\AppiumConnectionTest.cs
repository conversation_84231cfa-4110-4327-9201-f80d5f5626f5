using NUnit.Framework;
using OpenQA.Selenium.Appium.iOS;
using System;

namespace DrMuscle.UITests.Tests
{
    [TestFixture]
    public class AppiumConnectionTest : AppiumSetup
    {
        [Test]
        [Category("Smoke")]
        public void CanConnectToAppiumServer()
        {
            try
            {
                Console.WriteLine("Testing Appium server connection...");
                
                Assert.That(<PERSON>, Is.Not.Null, "Should be able to create Appium driver");
                Assert.That(Driver.SessionId, Is.Not.Null, "Should have a valid session ID");
                
                Console.WriteLine($"✅ Successfully connected to Appium. Session ID: {Driver.SessionId}");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Failed to connect to Appium server: {ex.Message}");
            }
        }
    }
}